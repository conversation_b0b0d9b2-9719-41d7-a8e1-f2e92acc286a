<template>
  <q-card 
    class="featured-profile-card cursor-pointer"
    :class="{ 'featured-profile-card--hover': !loading }"
    @click="handleClick"
  >
    <!-- Header Background -->
    <div class="profile-header" :style="`background: ${getProfileTypeGradient(profile.profile_type)}`">
      <!-- Profile Type Badge -->
      <div class="absolute-top-right q-ma-sm">
        <q-chip
          color="white"
          :text-color="getProfileTypeColor(profile.profile_type)"
          :icon="getProfileTypeIcon(profile.profile_type)"
          size="sm"
          class="type-badge"
        >
          {{ formatProfileType(profile.profile_type) }}
        </q-chip>
      </div>
    </div>

    <!-- Avatar Container -->
    <div class="avatar-container">
      <q-avatar size="80px" class="profile-avatar">
        <img 
          v-if="profile.avatar_url" 
          :src="profile.avatar_url" 
          :alt="profileName"
          @error="handleAvatarError"
        />
        <div v-else class="avatar-fallback">
          {{ getInitials(profileName) }}
        </div>
      </q-avatar>
    </div>

    <!-- Content Section -->
    <q-card-section class="featured-content text-center">
      <!-- Name -->
      <h4 class="profile-name q-mb-xs">
        {{ profileName }}
      </h4>

      <!-- Bio -->
      <p class="profile-bio text-grey-7 q-mb-sm">
        {{ profileBio }}
      </p>

      <!-- Stats Row -->
      <div class="profile-stats row justify-center q-gutter-md q-mb-sm">
        <div class="stat-item text-center">
          <div class="stat-value text-weight-bold">{{ completionPercentage }}%</div>
          <div class="stat-label text-caption text-grey-6">Complete</div>
        </div>
        <div class="stat-item text-center">
          <div class="stat-value text-weight-bold">{{ connectionCount }}</div>
          <div class="stat-label text-caption text-grey-6">Connections</div>
        </div>
      </div>

      <!-- Tags -->
      <div class="profile-tags q-mb-sm">
        <q-chip
          v-for="tag in displayTags"
          :key="tag"
          size="sm"
          color="grey-3"
          text-color="grey-8"
          class="q-ma-xs"
        >
          {{ tag }}
        </q-chip>
      </div>

      <!-- Action Button -->
      <q-btn
        color="primary"
        outline
        size="sm"
        label="View Profile"
        class="action-button"
        @click.stop="handleClick"
      />
    </q-card-section>

    <!-- Hover Effect Overlay -->
    <div class="hover-overlay absolute-full flex flex-center">
      <q-btn
        round
        color="white"
        text-color="primary"
        icon="person"
        size="lg"
        class="hover-button"
      />
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// Props
interface Props {
  profile: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  click: [profile: any];
}>();

// State
const loading = ref(false);

// Computed
const profileName = computed(() => {
  return props.profile.profile_name || 
         `${props.profile.first_name || ''} ${props.profile.last_name || ''}`.trim() ||
         'Anonymous User';
});

const profileBio = computed(() => {
  const bio = props.profile.bio || '';
  return bio.length > 80 ? bio.substring(0, 80) + '...' : bio || 'No bio available';
});

const completionPercentage = computed(() => {
  return props.profile.profile_completion || 0;
});

const connectionCount = computed(() => {
  // Use real connection count from profile data
  return props.profile.connection_count || props.profile.connections?.length || 0;
});

const displayTags = computed(() => {
  const tags = props.profile.tags || [];
  return tags.filter((tag: string) => tag !== 'featured').slice(0, 3);
});

// Methods
function getProfileTypeGradient(profileType: string): string {
  if (!profileType) return 'linear-gradient(135deg, #74B524 0%, #5A8F1C 100%)'; // Default green gradient

  const typeGradients: Record<string, string> = {
    'innovator': 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)', // Purple
    'investor': 'linear-gradient(135deg, #2196F3 0%, #1976D2 100%)', // Blue
    'mentor': 'linear-gradient(135deg, #2196F3 0%, #03A9F4 100%)', // Light Blue
    'professional': 'linear-gradient(135deg, #009688 0%, #00695C 100%)', // Teal
    'industry_expert': 'linear-gradient(135deg, #FF5722 0%, #D84315 100%)', // Deep Orange
    'academic_student': 'linear-gradient(135deg, #3F51B5 0%, #303F9F 100%)', // Indigo
    'academic_institution': 'linear-gradient(135deg, #03A9F4 0%, #0288D1 100%)', // Light Blue
    'organisation': 'linear-gradient(135deg, #FFC107 0%, #FF8F00 100%)' // Amber
  };

  return typeGradients[profileType.toLowerCase()] || 'linear-gradient(135deg, #74B524 0%, #5A8F1C 100%)';
}

function getProfileTypeColor(profileType: string): string {
  if (!profileType) return 'green';

  const typeColors: Record<string, string> = {
    'innovator': 'purple',
    'investor': 'blue',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  };

  return typeColors[profileType.toLowerCase()] || 'green';
}

function getProfileTypeIcon(profileType: string): string {
  const icons = {
    'innovator': 'lightbulb',
    'investor': 'trending_up',
    'mentor': 'school',
    'professional': 'work',
    'academic_student': 'school',
    'industry_expert': 'expert_mode',
    'academic_institution': 'account_balance',
    'organisation': 'business'
  };
  return icons[profileType as keyof typeof icons] || 'person';
}

function formatProfileType(profileType: string): string {
  if (!profileType) return 'User';
  return profileType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

function handleAvatarError(event: Event) {
  // Hide the broken image and show fallback
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
}

function handleClick() {
  emit('click', props.profile);
}
</script>

<style scoped>
.featured-profile-card {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 340px;
  display: flex;
  flex-direction: column;
}

.featured-profile-card--hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.profile-header {
  height: 80px;
  position: relative;
}

.type-badge {
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95) !important;
}

.avatar-container {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1;
}

.profile-avatar {
  border: 3px solid white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
}

.avatar-fallback {
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  color: white;
  font-weight: bold;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.featured-content {
  flex: 1;
  padding-top: 50px;
  display: flex;
  flex-direction: column;
}

.profile-name {
  font-size: 1.1rem;
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  color: #1a1a1a;
}

.profile-bio {
  font-size: 0.875rem;
  line-height: 1.3;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.profile-stats {
  margin: 8px 0;
}

.stat-item {
  min-width: 60px;
}

.stat-value {
  font-size: 1rem;
  color: #1a1a1a;
}

.stat-label {
  font-size: 0.7rem;
}

.profile-tags {
  min-height: 32px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-start;
}

.action-button {
  margin-top: auto;
}

.hover-overlay {
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(2px);
}

.featured-profile-card--hover:hover .hover-overlay {
  opacity: 1;
}

.hover-button {
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.featured-profile-card--hover:hover .hover-button {
  transform: scale(1);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .featured-profile-card {
    height: 320px;
  }
  
  .profile-name {
    font-size: 1rem;
  }
  
  .profile-bio {
    font-size: 0.8rem;
  }
}
</style>
