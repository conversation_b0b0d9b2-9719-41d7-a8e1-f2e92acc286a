<template>
  <div class="featured-events-section">
    <!-- Section Header -->
    <div class="featured-header q-mb-md">
      <div class="text-h6 text-weight-bold">Featured Events</div>
      <p class="text-caption text-grey-7 q-mb-none">Don't miss these upcoming events</p>
    </div>

    <!-- Horizontal Scrollable Container -->
    <div class="featured-scroll-container">
      <div class="featured-scroll-content" ref="scrollContainer">
        <!-- Featured Events -->
        <div v-for="event in featuredEvents" :key="event.id" class="featured-item">
          <featured-event-card
            :event="event"
            @click="handleEventClick"
          />
        </div>

        <!-- Loading placeholders -->
        <div v-if="loading" class="featured-item" v-for="n in 3" :key="`loading-${n}`">
          <featured-event-skeleton />
        </div>

        <!-- Empty state when no featured events -->
        <div v-if="!loading && featuredEvents.length === 0" class="featured-empty">
          <div class="text-center q-pa-lg">
            <q-icon name="event" size="3em" color="grey-5" />
            <p class="text-grey-6 q-mt-md">No featured events available</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import FeaturedEventCard from './FeaturedEventCard.vue';
import FeaturedEventSkeleton from './FeaturedEventSkeleton.vue';
import { usePostsStore } from '../../stores/posts';

// Props
interface Props {
  maxItems?: number;
}

const props = withDefaults(defineProps<Props>(), {
  maxItems: 6
});

// Emits
const emit = defineEmits<{
  eventClick: [event: any];
}>();

// Composables
const router = useRouter();
const postsStore = usePostsStore();

// State
const loading = ref(true);
const scrollContainer = ref<HTMLElement>();

// Computed
const featuredEvents = computed(() => {
  return postsStore.posts
    .filter(post =>
      (post.postType === 'EVENT' || post.subType === 'event') &&
      post.tags?.includes('featured') &&
      post.status === 'published'
    )
    .slice(0, props.maxItems);
});

// Methods
async function loadFeaturedEvents() {
  try {
    loading.value = true;
    console.log('Loading featured events...');
    
    // Load events with featured tags
    await postsStore.fetchPosts({
      postType: 'EVENT',
      subType: 'event',
      tags: ['featured'],
      status: 'published'
    });
    
    console.log('Featured events loaded:', featuredEvents.value.length);
  } catch (error) {
    console.error('Error loading featured events:', error);
  } finally {
    loading.value = false;
  }
}

function handleEventClick(event: any) {
  emit('eventClick', event);

  // Navigate to event details page
  router.push({ name: 'event-details', params: { id: event.id } });
}

// Lifecycle
onMounted(() => {
  console.log('FeaturedEventsSection mounted');
  loadFeaturedEvents();
});
</script>

<style scoped>
.featured-events-section {
  margin-bottom: 24px;
}

.featured-header {
  padding: 0 16px;
}

.featured-scroll-container {
  position: relative;
  overflow: hidden;
}

.featured-scroll-content {
  display: flex;
  gap: 16px;
  padding: 0 16px 16px 16px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.featured-scroll-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.featured-item {
  flex: 0 0 360px;
  min-width: 360px;
}

.featured-empty {
  flex: 1;
  min-width: 100%;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .featured-item {
    flex: 0 0 320px;
    min-width: 320px;
  }

  .featured-scroll-content {
    gap: 12px;
    padding: 0 12px 12px 12px;
  }
}

@media (max-width: 480px) {
  .featured-item {
    flex: 0 0 280px;
    min-width: 280px;
  }
}
</style>
